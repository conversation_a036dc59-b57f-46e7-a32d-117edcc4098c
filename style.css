td {
	text-align: left;
	vertical-align: top;
	font-family:<PERSON><PERSON><PERSON>;
	font-size:14px;
	color:#000000;
	line-height:18px;
}


	
.input{background-color:#fff;
	width:250px;
	height:17px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#584c3f;
	border:1px solid #7b8490;}
	
.message{background-color:#fff;
	width:250px;
	height:90px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:11px;
	color:#584c3f;
	border:1px solid #7b8490;}
	



body {margin:0px; padding:0px; background-color:#ffffff;}
.header {background-image:url(images/header.jpg); background-repeat:no-repeat;}
.top {background-image:url(images/top.jpg); background-repeat:no-repeat;}
.bottom {
	background-image:url(images/bottom.jpg);
	background-repeat:repeat-x;
	color: #333;
}

.menu         { font-family: <PERSON>homa, serif; font-size:14px; color: #000000; text-align:center; display:block; width:106px; padding-top:10px; height:27px; text-decoration:none; float:left; font-weight:bold;}
.menu:link    { font-family: Tahoma, serif; font-size:14px; color: #000000; text-align:center; display:block; width:106px; padding-top:10px; height:27px; text-decoration:none; float:left; font-weight:bold;}
.menu:hover   { font-family: Tahoma, serif; font-size:14px; color: #000000; text-align:center; display:block; width:106px; padding-top:10px; height:27px; text-decoration:none; float:left; background-image:url(images/batton.html); background-repeat:no-repeat; font-weight:bold;}
.menu:active  { font-family: Tahoma, serif; font-size:14px; color: #000000; text-align:center; display:block; width:106px; padding-top:10px; height:27px; text-decoration:none; float:left; font-weight:bold;}

.menu_bottom         { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:none;}
.menu_bottom:link    { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:none;}
.menu_bottom:hover   { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:underline;}
.menu_bottom:active  { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:none;}

.read_top         { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:none;}
.read_top:link    { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:none;}
.read_top:hover   { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:underline;}
.read_top:active  { font-family: Verdana, serif; font-size: 14px; color: #000000; font-weight:bold; text-decoration:none;}

.read         { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:underline;}
.read:link    { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:underline;}
.read:hover   { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:none;}
.read:active  { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:underline;}

.read1         { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:underline;}
.read1:link    { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:underline;}
.read1:hover   { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:none;}
.read1:active  { font-family: Verdana, serif; font-size: 14px; color: #000000; text-decoration:underline;}




.name { font-family:Tahoma, serif; font-size:22px;}
.orang { color:#ff0000;}


.pictures { float:left; margin-right:15px;}
.logo {margin-top:30px; margin-left:75px; margin-right:15px;}
.str {margin-right:10px;}
.str1 { list-style-image: url('images/str.html'); padding-left:10px; margin-left:10px; line-height:20px; margin-top:0px; margin-bottom:0px;}
.str2 { list-style-image: url('images/str.html'); padding-left:20px; margin-left:20px; line-height:20px; margin-top:0px; margin-bottom:0px;}

.menu_bot {margin-top:20px; padding-bottom:20px; margin-right:25px; float:right;}
.blok {
	margin-top:30px;
	margin-bottom:25px;
	margin-right:15px;
	color: #333;
	font-family: Tahoma, Geneva, sans-serif;
}
.blok1 {margin-top:30px; margin-bottom:25px; margin-left:15px;}
.mine {
	font-family: Georgia, "Times New Roman", Times, serif;
	font-size: 18px;
	font-style: normal;
	font-weight: bold;
	color: #666;
	line-height: normal;
	font-variant: normal;
	letter-spacing: normal;
	word-spacing: normal;
	white-space: normal;
}
.bottom p {
	font-family: "Times New Roman", Times, serif;
}
